#ifndef STREAMDATAPROCESSOR_H
#define STREAMDATAPROCESSOR_H

#include <QObject>
#include <QThread>
#include <QTimer>
#include <QMutex>
#include <QHash>
#include <QSharedPointer>
#include <QQueue>
#include <QElapsedTimer>
#include <QWaitCondition>
#include <atomic>
#include "businessprocess/dataitem.h"
#include <QThreadPool>

class UploadDataHandler;
class SyncTask;

/**
 * @brief 流式数据处理器 - UploadDataBackgroundProcessor的改进版本
 * 采用事件驱动 + 阈值控制的方式，提供更好的实时性和性能
 */
class StreamDataProcessor : public QObject
{
    Q_OBJECT

public:
    /**
     * @brief 传感器数据缓存结构
     */
    struct SensorDataCache {
        QString sensorId;
        QList<QSharedPointer<DataItem>> rawDataItems;      // 原始数据项（未去重）
        QList<QSharedPointer<DataItem>> filteredDataItems; // 去重后的数据项
        int totalOriginalCount;                            // 累计原始数据数量
        
        SensorDataCache() : totalOriginalCount(0) {}
        explicit SensorDataCache(const QString& id) : sensorId(id), totalOriginalCount(0) {}
    };

    /**
     * @brief 传感器数据队列结构
     */
    struct SensorDataQueue {
        QString sensorId;
        QQueue<QSharedPointer<DataItem>> dataItems;
        QElapsedTimer lastProcessTime;
        std::atomic<int> pendingCount{0};
        QMutex queueMutex;
        
        explicit SensorDataQueue(const QString& id) : sensorId(id) {
            lastProcessTime.start();
        }
    };

    /**
     * @brief 处理配置参数
     */
    struct ProcessingConfig {
        int batchThreshold = 25;          // 批处理阈值
        int maxWaitTime = 3000;           // 最大等待时间(ms)
        int maxCacheSize = 10000;         // 最大缓存大小
        int workerThreads = 3;            // 工作线程数
        int dbBatchSize = 20;             // 数据库批量大小
        bool enableDynamicAdjust = true;  // 是否启用动态调整
    };

    explicit StreamDataProcessor(QObject *parent = nullptr);
    ~StreamDataProcessor();

    /**
     * @brief 启动处理器
     */
    void start();

    /**
     * @brief 停止处理器
     */
    void stop();

    /**
     * @brief 添加待处理的数据项
     * @param sensorId 传感器ID
     * @param dataItems 数据项列表
     * @return 是否成功添加（false表示缓存满，需要背压控制）
     */
    bool addDataItems(const QString& sensorId, const QList<QSharedPointer<DataItem>>& dataItems);

    /**
     * @brief 设置每个传感器的批量处理数量（兼容接口）
     * @param batchSize 批量处理数量
     */
    void setBatchSize(int batchSize) { 
        m_config.dbBatchSize = batchSize; 
        m_config.batchThreshold = batchSize + 5; // 稍微大一点的阈值
    }

    /**
     * @brief 设置处理间隔时间(毫秒)（兼容接口，实际用作最大等待时间）
     * @param interval 间隔时间
     */
    void setProcessInterval(int interval) { 
        m_config.maxWaitTime = interval; 
    }

    /**
     * @brief 设置同步任务引用
     * @param syncTask 同步任务
     */
    void setSyncTask(QSharedPointer<SyncTask> syncTask) { m_syncTask = syncTask; }

    /**
     * @brief 获取性能统计信息
     */
    struct PerformanceStats {
        int totalProcessed = 0;
        int avgProcessingTime = 0;
        int currentCacheSize = 0;
        int activeThreads = 0;
    };
    PerformanceStats getPerformanceStats() const;

signals:
    /**
     * @brief 数据处理完成信号
     */
    void dataProcessed();

    /**
     * @brief 背压信号（缓存满时发出）
     * @param sensorId 传感器ID
     * @param backpressure 是否需要背压控制
     */
    void backpressureSignal(const QString& sensorId, bool backpressure);

private:
    /**
     * @brief 检查是否应该触发处理
     * @param queue 传感器数据队列
     * @return 是否应该处理
     */
    bool shouldTriggerProcessing(QSharedPointer<SensorDataQueue> queue);

    /**
     * @brief 触发处理指定传感器的数据
     * @param sensorId 传感器ID
     */
    void triggerProcessing(const QString& sensorId);

    /**
     * @brief 处理传感器数据
     * @param sensorId 传感器ID
     */
    void processSensorData(const QString& sensorId);

    /**
     * @brief 处理单个传感器的缓存数据
     * @param cache 传感器数据缓存
     */
    void processSensorCache(QSharedPointer<SensorDataCache> cache);

    /**
     * @brief 对数据项进行去重
     * @param sensorId 传感器ID
     * @param dataItems 原始数据项
     * @return 去重后的数据项
     */
    QList<QSharedPointer<DataItem>> deduplicateData(const QString& sensorId, 
                                                    const QList<QSharedPointer<DataItem>>& dataItems);

    /**
     * @brief 获取或创建传感器数据队列
     * @param sensorId 传感器ID
     * @return 传感器数据队列
     */
    QSharedPointer<SensorDataQueue> getSensorQueue(const QString& sensorId);

    /**
     * @brief 动态调整处理参数
     */
    void adjustParameters();

    /**
     * @brief 更新性能统计
     * @param processingTime 处理时间
     * @param itemCount 处理的数据项数量
     */
    void updatePerformanceStats(int processingTime, int itemCount);

private:
    // 配置参数
    ProcessingConfig m_config;
    
    // 数据结构
    QHash<QString, QSharedPointer<SensorDataQueue>> m_sensorQueues;
    QMutex m_queuesMutex;
    
    // 线程管理
    QThreadPool m_threadPool;
    std::atomic<bool> m_running{false};
    
    // 资源管理
    std::atomic<int> m_totalCacheSize{0};
    
    // 性能监控
    QElapsedTimer m_performanceTimer;
    std::atomic<int> m_processedCount{0};
    std::atomic<int> m_avgProcessingTime{100};
    std::atomic<int> m_activeThreads{0};
    
    // 动态调整定时器
    QTimer* m_adjustTimer;
    
    // 同步任务引用
    QSharedPointer<SyncTask> m_syncTask;
};

#endif // STREAMDATAPROCESSOR_H
