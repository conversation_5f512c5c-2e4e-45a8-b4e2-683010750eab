#include "streamdataprocessor.h"
#include "uploaddatahandler.h"
#include "synctask.h"
#include "log.h"
#include <QThread>
#include <QMutexLocker>
#include <QCoreApplication>
#include <QtConcurrent>

StreamDataProcessor::StreamDataProcessor(QObject *parent)
    : QObject(parent)
    , m_adjustTimer(nullptr)
{
    // 初始化性能计时器
    m_performanceTimer.start();
    
    // 创建动态调整定时器
    m_adjustTimer = new QTimer(this);
    m_adjustTimer->setInterval(10000); // 10秒调整一次
    connect(m_adjustTimer, &QTimer::timeout, this, &StreamDataProcessor::adjustParameters);
}

StreamDataProcessor::~StreamDataProcessor()
{
    stop();
}

void StreamDataProcessor::start()
{
    if (m_running.load()) {
        logWarnning("StreamDataProcessor::start - Processor is already running");
        return;
    }

    logInfo("StreamDataProcessor::start - Starting stream data processor");

    // 设置线程池
    m_threadPool.setMaxThreadCount(m_config.workerThreads);
    
    // 启动运行状态
    m_running.store(true);
    
    // 启动动态调整定时器
    if (m_config.enableDynamicAdjust) {
        m_adjustTimer->start();
    }
    
    logInfo(QString("StreamDataProcessor::start - Started with config: batchThreshold=%1, maxWaitTime=%2ms, workerThreads=%3")
            .arg(m_config.batchThreshold).arg(m_config.maxWaitTime).arg(m_config.workerThreads));
}

void StreamDataProcessor::stop()
{
    if (!m_running.load()) {
        logWarnning("StreamDataProcessor::stop - Processor is not running");
        return;
    }

    logInfo("StreamDataProcessor::stop - Stopping stream data processor");
    
    // 停止运行状态
    m_running.store(false);
    
    // 停止动态调整定时器
    if (m_adjustTimer) {
        m_adjustTimer->stop();
    }
    
    // 等待所有任务完成
    m_threadPool.waitForDone(5000);
    
    // 处理剩余数据
    QMutexLocker locker(&m_queuesMutex);
    for (auto it = m_sensorQueues.begin(); it != m_sensorQueues.end(); ++it) {
        if (it.value()->pendingCount.load() > 0) {
            logInfo(QString("StreamDataProcessor::stop - Processing remaining data for sensor [%1]: %2 items")
                    .arg(it.key()).arg(it.value()->pendingCount.load()));
            processSensorData(it.key());
        }
    }
    
    // 清空队列
    m_sensorQueues.clear();
    m_totalCacheSize.store(0);
    
    logInfo("StreamDataProcessor::stop - Stream data processor stopped");
}

bool StreamDataProcessor::addDataItems(const QString& sensorId, 
                                      const QList<QSharedPointer<DataItem>>& dataItems)
{
    if (!m_running.load()) {
        logWarnning("StreamDataProcessor::addDataItems - Processor is not running");
        return false;
    }
    
    // 检查缓存是否已满
    int currentSize = m_totalCacheSize.load();
    if (currentSize + dataItems.size() > m_config.maxCacheSize) {
        logWarnning(QString("StreamDataProcessor::addDataItems - Cache full, current: %1, adding: %2, max: %3")
                    .arg(currentSize).arg(dataItems.size()).arg(m_config.maxCacheSize));
        emit backpressureSignal(sensorId, true);
        return false;
    }
    
    // 获取传感器队列
    auto queue = getSensorQueue(sensorId);
    
    // 添加数据到队列
    {
        QMutexLocker queueLocker(&queue->queueMutex);
        for (const auto& item : dataItems) {
            queue->dataItems.enqueue(item);
        }
        queue->pendingCount += dataItems.size();
    }
    
    // 更新总缓存大小
    m_totalCacheSize += dataItems.size();
    
    logTrace(QString("StreamDataProcessor::addDataItems - Added %1 items for sensor [%2], pending: %3, total cache: %4")
             .arg(dataItems.size()).arg(sensorId).arg(queue->pendingCount.load()).arg(m_totalCacheSize.load()));
    
    // 检查是否需要触发处理
    if (shouldTriggerProcessing(queue)) {
        triggerProcessing(sensorId);
    }
    
    emit backpressureSignal(sensorId, false);
    return true;
}

QSharedPointer<StreamDataProcessor::SensorDataQueue> StreamDataProcessor::getSensorQueue(const QString& sensorId)
{
    QMutexLocker locker(&m_queuesMutex);
    
    if (!m_sensorQueues.contains(sensorId)) {
        m_sensorQueues[sensorId] = QSharedPointer<SensorDataQueue>::create(sensorId);
        logTrace(QString("StreamDataProcessor::getSensorQueue - Created new queue for sensor [%1]").arg(sensorId));
    }
    
    return m_sensorQueues[sensorId];
}

bool StreamDataProcessor::shouldTriggerProcessing(QSharedPointer<SensorDataQueue> queue)
{
    // 数据量阈值触发
    if (queue->pendingCount.load() >= m_config.batchThreshold) {
        logTrace(QString("StreamDataProcessor::shouldTriggerProcessing - Triggered by count threshold: %1 >= %2")
                 .arg(queue->pendingCount.load()).arg(m_config.batchThreshold));
        return true;
    }
    
    // 时间阈值触发
    if (queue->lastProcessTime.elapsed() > m_config.maxWaitTime && queue->pendingCount.load() > 0) {
        logTrace(QString("StreamDataProcessor::shouldTriggerProcessing - Triggered by time threshold: %1ms > %2ms, count: %3")
                 .arg(queue->lastProcessTime.elapsed()).arg(m_config.maxWaitTime).arg(queue->pendingCount.load()));
        return true;
    }
    
    return false;
}

void StreamDataProcessor::triggerProcessing(const QString& sensorId)
{
    if (!m_running.load()) {
        return;
    }
    
    // 提交异步任务到线程池
    auto task = [this, sensorId]() {
        m_activeThreads++;
        try {
            processSensorData(sensorId);
        } catch (const std::exception& e) {
            logError(QString("StreamDataProcessor::triggerProcessing - Error processing sensor [%1]: %2")
                     .arg(sensorId).arg(e.what()));
        } catch (...) {
            logError(QString("StreamDataProcessor::triggerProcessing - Unknown error processing sensor [%1]")
                     .arg(sensorId));
        }
        m_activeThreads--;
    };
    
    QtConcurrent::run(&m_threadPool, task);
}

void StreamDataProcessor::processSensorData(const QString& sensorId)
{
    QElapsedTimer processingTimer;
    processingTimer.start();
    
    auto queue = getSensorQueue(sensorId);
    
    // 从队列中提取数据
    QList<QSharedPointer<DataItem>> dataToProcess;
    int extractedCount = 0;
    
    {
        QMutexLocker queueLocker(&queue->queueMutex);
        
        // 提取数据，最多提取batchThreshold数量
        while (!queue->dataItems.isEmpty() && extractedCount < m_config.batchThreshold) {
            dataToProcess.append(queue->dataItems.dequeue());
            extractedCount++;
        }
        
        queue->pendingCount -= extractedCount;
        queue->lastProcessTime.restart();
    }
    
    if (dataToProcess.isEmpty()) {
        return;
    }
    
    logInfo(QString("StreamDataProcessor::processSensorData - Processing sensor [%1], %2 items")
            .arg(sensorId).arg(dataToProcess.size()));
    
    // 创建缓存对象进行处理
    auto cache = QSharedPointer<SensorDataCache>::create(sensorId);
    cache->rawDataItems = dataToProcess;
    cache->totalOriginalCount = dataToProcess.size();
    
    // 处理缓存数据
    processSensorCache(cache);
    
    // 更新缓存大小
    m_totalCacheSize -= extractedCount;
    
    // 更新性能统计
    int processingTime = processingTimer.elapsed();
    updatePerformanceStats(processingTime, extractedCount);
    
    logTrace(QString("StreamDataProcessor::processSensorData - Completed sensor [%1], processed %2 items in %3ms")
             .arg(sensorId).arg(extractedCount).arg(processingTime));
}

StreamDataProcessor::PerformanceStats StreamDataProcessor::getPerformanceStats() const
{
    PerformanceStats stats;
    stats.totalProcessed = m_processedCount.load();
    stats.avgProcessingTime = m_avgProcessingTime.load();
    stats.currentCacheSize = m_totalCacheSize.load();
    stats.activeThreads = m_activeThreads.load();
    return stats;
}

void StreamDataProcessor::updatePerformanceStats(int processingTime, int itemCount)
{
    m_processedCount += itemCount;
    
    // 计算移动平均处理时间
    int currentAvg = m_avgProcessingTime.load();
    int newAvg = (currentAvg * 9 + processingTime) / 10; // 简单的移动平均
    m_avgProcessingTime.store(newAvg);
}

void StreamDataProcessor::adjustParameters()
{
    if (!m_config.enableDynamicAdjust || !m_running.load()) {
        return;
    }

    int avgTime = m_avgProcessingTime.load();
    int cacheSize = m_totalCacheSize.load();

    logTrace(QString("StreamDataProcessor::adjustParameters - Current stats: avgTime=%1ms, cacheSize=%2, threshold=%3")
             .arg(avgTime).arg(cacheSize).arg(m_config.batchThreshold));

    // 根据处理时间调整批次大小
    if (avgTime > 500) {
        // 处理慢，减小批次
        m_config.batchThreshold = std::max(10, m_config.batchThreshold - 5);
        logInfo(QString("StreamDataProcessor::adjustParameters - Reduced batch threshold to %1 (slow processing)")
                .arg(m_config.batchThreshold));
    } else if (avgTime < 100 && cacheSize < m_config.maxCacheSize / 2) {
        // 处理快且缓存不满，增大批次
        m_config.batchThreshold = std::min(50, m_config.batchThreshold + 5);
        logInfo(QString("StreamDataProcessor::adjustParameters - Increased batch threshold to %1 (fast processing)")
                .arg(m_config.batchThreshold));
    }

    // 根据缓存使用率调整等待时间
    float cacheUsage = float(cacheSize) / m_config.maxCacheSize;
    if (cacheUsage > 0.8f) {
        // 缓存使用率高，减少等待时间
        m_config.maxWaitTime = std::max(1000, m_config.maxWaitTime - 500);
        logInfo(QString("StreamDataProcessor::adjustParameters - Reduced wait time to %1ms (high cache usage)")
                .arg(m_config.maxWaitTime));
    } else if (cacheUsage < 0.3f) {
        // 缓存使用率低，增加等待时间
        m_config.maxWaitTime = std::min(5000, m_config.maxWaitTime + 500);
        logInfo(QString("StreamDataProcessor::adjustParameters - Increased wait time to %1ms (low cache usage)")
                .arg(m_config.maxWaitTime));
    }
}

void StreamDataProcessor::processSensorCache(QSharedPointer<SensorDataCache> cache)
{
    logInfo(QString("StreamDataProcessor::processSensorCache - Processing sensor [%1], %2 raw items")
            .arg(cache->sensorId).arg(cache->rawDataItems.size()));

    // 数据去重
    logTrace(QString("StreamDataProcessor::processSensorCache - Deduplicating data for sensor [%1]")
             .arg(cache->sensorId));
    cache->filteredDataItems = deduplicateData(cache->sensorId, cache->rawDataItems);

    int duplicateCount = cache->rawDataItems.size() - cache->filteredDataItems.size();
    logInfo(QString("StreamDataProcessor::processSensorCache - Sensor [%1], deduplication: total [%2], new [%3], duplicates [%4]")
            .arg(cache->sensorId).arg(cache->rawDataItems.size()).arg(cache->filteredDataItems.size()).arg(duplicateCount));

    // 清空原始数据, 释放内存
    cache->rawDataItems.clear();

    if (cache->filteredDataItems.isEmpty()) {
        logTrace(QString("StreamDataProcessor::processSensorCache - Sensor [%1], no new data to process")
                 .arg(cache->sensorId));

        // 更新同步计数（使用原始数量）
        if (!m_syncTask.isNull()) {
            m_syncTask->updateSensorSyncedItemCount(cache->sensorId, cache->totalOriginalCount);
            emit dataProcessed();
        }
        return;
    }

    if (duplicateCount > 0) {
        // 更新同步计数（重复数据量）
        if (!m_syncTask.isNull()) {
            m_syncTask->updateSensorSyncedItemCount(cache->sensorId, duplicateCount);
            logTrace(QString("StreamDataProcessor::processSensorCache - Updated sync count for sensor [%1], duplicates count: %2")
                     .arg(cache->sensorId).arg(duplicateCount));
            emit dataProcessed();
        }
    }

    // 批量解析和入库
    logTrace(QString("StreamDataProcessor::processSensorCache - Processing %1 filtered items for sensor [%2]")
             .arg(cache->filteredDataItems.size()).arg(cache->sensorId));

    const int dbBatchSize = m_config.dbBatchSize;
    QList<QSharedPointer<DataItem>> currentBatch;
    int totalProcessed = 0;
    int successfulBatches = 0;
    int failedBatches = 0;

    for (int i = 0; i < cache->filteredDataItems.size(); ++i) {
        currentBatch.append(cache->filteredDataItems[i]);
        cache->filteredDataItems[i].clear(); // 立即释放智能指针

        if (currentBatch.size() >= dbBatchSize || i == cache->filteredDataItems.size() - 1) {
            int batchNumber = (totalProcessed / dbBatchSize) + 1;
            int currentBatchSize = currentBatch.size();

            logTrace(QString("StreamDataProcessor::processSensorCache - Processing batch %1/%2 for sensor [%3], batch size: %4")
                     .arg(batchNumber)
                     .arg((cache->filteredDataItems.size() + dbBatchSize - 1) / dbBatchSize)
                     .arg(cache->sensorId)
                     .arg(currentBatchSize));

            // 处理当前批次
            UploadDataHandler batchHandler;
            batchHandler.setSensorId(cache->sensorId);
            batchHandler.addData(currentBatch);

            bool parseResult = batchHandler.parseData();
            if (!parseResult) {
                logError(QString("StreamDataProcessor::processSensorCache - Parse failed for batch %1, sensor [%2], batch size: %3")
                         .arg(batchNumber).arg(cache->sensorId).arg(currentBatchSize));
                failedBatches++;
            } else {
                logTrace(QString("StreamDataProcessor::processSensorCache - Parse successful for batch %1, sensor [%2]")
                         .arg(batchNumber).arg(cache->sensorId));
            }

            bool saveResult = batchHandler.saveDataToDB();
            if (!saveResult) {
                logError(QString("StreamDataProcessor::processSensorCache - Save to DB failed for batch %1, sensor [%2], batch size: %3")
                         .arg(batchNumber).arg(cache->sensorId).arg(currentBatchSize));
                failedBatches++;
            } else {
                logTrace(QString("StreamDataProcessor::processSensorCache - Save to DB successful for batch %1, sensor [%2]")
                         .arg(batchNumber).arg(cache->sensorId));
                successfulBatches++;
            }

            // 统计处理结果
            if (parseResult && saveResult) {
                totalProcessed += currentBatchSize;
                logTrace(QString("StreamDataProcessor::processSensorCache - Batch %1 completed successfully, processed %2/%3 items for sensor [%4]")
                         .arg(batchNumber).arg(totalProcessed).arg(cache->filteredDataItems.size()).arg(cache->sensorId));
            }

            // 更新同步计数
            if (!m_syncTask.isNull()) {
                m_syncTask->updateSensorSyncedItemCount(cache->sensorId, currentBatchSize);
                logTrace(QString("StreamDataProcessor::processSensorCache - Updated sync count for sensor [%1], currentBatchSize: %2")
                         .arg(cache->sensorId).arg(currentBatchSize));
                emit dataProcessed();
            }

            // 释放内存
            currentBatch.clear();

            if (!m_running.load()) {
                logWarnning(QString("StreamDataProcessor::processSensorCache - Processing stopped, sensor [%1], processed %2/%3 items")
                            .arg(cache->sensorId).arg(totalProcessed).arg(cache->filteredDataItems.size()));
                break;
            }
        }
    }

    // 输出最终处理结果统计
    logInfo(QString("StreamDataProcessor::processSensorCache - Sensor [%1] processing completed: total items [%2], processed [%3], successful batches [%4], failed batches [%5]")
            .arg(cache->sensorId)
            .arg(cache->filteredDataItems.size())
            .arg(totalProcessed)
            .arg(successfulBatches)
            .arg(failedBatches));
}

QList<QSharedPointer<DataItem>> StreamDataProcessor::deduplicateData(const QString& sensorId,
                                                                    const QList<QSharedPointer<DataItem>>& dataItems)
{
    if (m_syncTask.isNull()) {
        logWarnning("StreamDataProcessor::deduplicateData - SyncTask is null, cannot deduplicate");
        return dataItems;
    }

    // 获取传感器已存在的数据ID信息
    const SyncTask::SensorSyncInfo sensorSyncInfo = m_syncTask->getSensorSyncInfo(sensorId);
    logTrace(QString("StreamDataProcessor::deduplicateData - Sensor [%1] has %2 existing data IDs")
             .arg(sensorId).arg(sensorSyncInfo.existingDataIds.size()));

    QList<QSharedPointer<DataItem>> filteredDataItems;

    for (int i = 0; i < dataItems.size(); ++i) {
        const auto& dataItem = dataItems[i];
        if (!sensorSyncInfo.existingDataIds.contains(dataItem->getDataId())) {
            filteredDataItems.append(dataItem);
        } else {
            logTrace(QString("StreamDataProcessor::deduplicateData - Sensor [%1], Data ID [%2] already exists, skipped")
                     .arg(sensorId, QString::number(dataItem->getDataId())));
        }
    }

    return filteredDataItems;
}
