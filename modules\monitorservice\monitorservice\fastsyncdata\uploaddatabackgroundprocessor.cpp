#include "uploaddatabackgroundprocessor.h"
#include "uploaddatahandler.h"
#include "synctask.h"
#include "log.h"
#include <QThread>
#include <QMutexLocker>
#include <QCoreApplication>
#include <QtConcurrent>

UploadDataBackgroundProcessor::UploadDataBackgroundProcessor(QObject *parent)
    : QObject(parent)
    , m_workerThread(nullptr)
    , m_processTimer(nullptr)
    , m_spaceSemaphore(MAX_CaCHE_SIZE)
    , m_batchSize(20)
    , m_processInterval(15*1000)
    , m_isRunning(false)
{
}

UploadDataBackgroundProcessor::~UploadDataBackgroundProcessor()
{
    stop();
}

void UploadDataBackgroundProcessor::start()
{
    if (m_isRunning) {
        logWarnning("UploadDataBackgroundProcessor::start - Background processor is already running");
        return;
    }

    logInfo("UploadDataBackgroundProcessor::start - Starting background processor");

    // 创建工作线程
    m_workerThread = new QThread(this);
    m_workerThread->setPriority(QThread::LowPriority);
    
    // 创建定时器并移动到工作线程
    m_processTimer = new QTimer();
    m_processTimer->setInterval(m_processInterval);
    m_processTimer->moveToThread(m_workerThread);
    
    // 将当前对象移动到工作线程
    this->moveToThread(m_workerThread);
    
    // 连接信号槽
    connect(m_workerThread, &QThread::started, this, [this]() {
        m_processTimer->start();
        logInfo("UploadDataBackgroundProcessor - Worker thread started");
    });
    
    connect(m_processTimer, &QTimer::timeout, this, &UploadDataBackgroundProcessor::onProcessTimer);
    connect(m_workerThread, &QThread::finished, m_processTimer, &QTimer::deleteLater);

    m_workerThread->start();
    m_threadPool.setMaxThreadCount(6);
    m_isRunning = true;
    
    logInfo("UploadDataBackgroundProcessor::start - Background processor started successfully");
}

void UploadDataBackgroundProcessor::stop()
{
    if (!m_isRunning) {
        logWarnning("UploadDataBackgroundProcessor::stop - Background processor is not running");
        return;
    }

    logInfo("UploadDataBackgroundProcessor::stop - Stopping background processor");
    m_isRunning = false;

    if (m_processTimer) {
        QMetaObject::invokeMethod(m_processTimer, "stop", Qt::BlockingQueuedConnection);
    }

    if (m_workerThread) {
        // 处理剩余缓存数据
        QMetaObject::invokeMethod(this, "processCache", Qt::BlockingQueuedConnection);
        
        m_workerThread->quit();
        m_workerThread->wait(9000);
        
        if (m_workerThread->isRunning()) {
            logWarnning("UploadDataBackgroundProcessor::stop - Force terminating worker thread");
            m_workerThread->terminate();
            m_workerThread->wait(2000);
        }
        
        m_workerThread->deleteLater();
        m_workerThread = nullptr;
    }

    // 清空缓存
    QMutexLocker locker(&m_cacheMutex);
    m_sensorCacheMap.clear();

    logInfo("UploadDataBackgroundProcessor::stop - Background processor stopped");
}

void UploadDataBackgroundProcessor::addDataItems(const QString& sensorId, 
                                                 const QList<QSharedPointer<DataItem>>& dataItems)
{
    // 检查队列是否已满
    if (!m_spaceSemaphore.tryAcquire(dataItems.size(), 18*1000)) {  // 18s超时
        logWarnning(QString("Queue full, dropping data for sensor: %1").arg(sensorId));
        return;
    }
    
    QMutexLocker locker(&m_cacheMutex);
    
    // 获取或创建传感器缓存
    if (!m_sensorCacheMap.contains(sensorId)) {
        m_sensorCacheMap[sensorId] = QSharedPointer<SensorDataCache>::create(sensorId);
    }
    
    auto& cache = m_sensorCacheMap[sensorId];
    cache->rawDataItems.append(dataItems);
    cache->totalOriginalCount += dataItems.size();
    
    logTrace(QString("UploadDataBackgroundProcessor::addDataItems - Added %1 items for sensor [%2], cache size: %3")
             .arg(dataItems.size()).arg(sensorId).arg(cache->rawDataItems.size()));
}

void UploadDataBackgroundProcessor::setProcessInterval(int interval)
{
    m_processInterval = interval;
    if (m_processTimer) {
        m_processTimer->setInterval(interval);
    }
}

void UploadDataBackgroundProcessor::onProcessTimer()
{
    processCache();
}

void UploadDataBackgroundProcessor::processCache()
{    
    QList<QSharedPointer<SensorDataCache>> cacheToProcess;
    
    // 获取需要处理的缓存数据
    {
        QMutexLocker locker(&m_cacheMutex);
        
        for (auto it = m_sensorCacheMap.begin(); it != m_sensorCacheMap.end();) {
            QSharedPointer<SensorDataCache>& cache = it.value();
            
            if(!m_isRunning)
            {
                cacheToProcess.clear();
                logInfo("UploadDataBackgroundProcessor::processCache - Stop processing cache");
                break;
            }

            cacheToProcess.append(cache);
            it = m_sensorCacheMap.erase(it);
            // 释放信号量
            //m_spaceSemaphore.release(cache->rawDataItems.size());
        }
    }
    
    // 处理缓存数据
    if(!cacheToProcess.isEmpty())
    {
        QList<QFuture<void>> futures;

        // 为每个传感器缓存创建独立的异步任务
        for (auto& cache : cacheToProcess) {

            if(!m_isRunning)
            {
                logInfo("UploadDataBackgroundProcessor::processCache - Stop processing cache");
                break;
            }

            auto future = QtConcurrent::run(&m_threadPool, [this, cache]() {
                try {
                    processSensorCache(cache);
                } catch (const std::exception& e) {
                    logError(QString("Error processing sensor cache [%1]: %2")
                             .arg(cache->sensorId).arg(e.what()));
                }
                catch (...) {
                    logError(QString("Unknown error processing sensor cache [%1]")
                             .arg(cache->sensorId));
                }
            });
            futures.append(future);
        }

        for (auto& future : futures) {
            future.waitForFinished();
        }
    }
}

void UploadDataBackgroundProcessor::processSensorCache(QSharedPointer<SensorDataCache> cache)
{
    logInfo(QString("UploadDataBackgroundProcessor::processSensorCache - Processing sensor [%1], %2 raw items")
            .arg(cache->sensorId).arg(cache->rawDataItems.size())) <<", current thread:" << QThread::currentThread();
    
    // 数据去重
    logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Deduplicating data for sensor [%1]")
             .arg(cache->sensorId));
    cache->filteredDataItems = deduplicateData(cache->sensorId, cache->rawDataItems);
    
    int duplicateCount = cache->rawDataItems.size() - cache->filteredDataItems.size();
    logInfo(QString("UploadDataBackgroundProcessor::processSensorCache - Sensor [%1], deduplication: total [%2], new [%3], duplicates [%4]")
            .arg(cache->sensorId).arg(cache->rawDataItems.size()).arg(cache->filteredDataItems.size()).arg(duplicateCount));

    // 清空原始数据, 释放内存
    cache->rawDataItems.clear();

    //yieldCpu();
    
    if (cache->filteredDataItems.isEmpty()) {
        logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Sensor [%1], no new data to process")
                 .arg(cache->sensorId));
        
        // 更新同步计数（使用原始数量）
        if (!m_syncTask.isNull()) {
            m_syncTask->updateSensorSyncedItemCount(cache->sensorId, cache->totalOriginalCount);
            emit dataProcessed();
        }

        // 释放信号量
        m_spaceSemaphore.release(cache->totalOriginalCount);

        return;
    }

    if(0 != duplicateCount)
    {
        // 更新同步计数（重复数据量）
        if (!m_syncTask.isNull()) {
            m_syncTask->updateSensorSyncedItemCount(cache->sensorId, duplicateCount);
            logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Updated sync count for sensor [%1], duplicates count: %2")
                     .arg(cache->sensorId).arg(duplicateCount));
            emit dataProcessed();
        }

        // 释放信号量
        m_spaceSemaphore.release(duplicateCount);
    }
    
    // 批量解析和入库
    logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Processing %1 filtered items for sensor [%2]")
             .arg(cache->filteredDataItems.size()).arg(cache->sensorId));

    const int dbBatchSize = m_batchSize; // 数据库批量大小
    QList<QSharedPointer<DataItem>> currentBatch;
    int totalProcessed = 0;
    int successfulBatches = 0;
    int failedBatches = 0;
    
    for (int i = 0; i < cache->filteredDataItems.size(); ++i) {
        currentBatch.append(cache->filteredDataItems[i]);
        cache->filteredDataItems[i].clear(); // 立即释放智能指针

        if (currentBatch.size() >= dbBatchSize || i == cache->filteredDataItems.size() - 1) {
            int batchNumber = (totalProcessed / dbBatchSize) + 1;
            int currentBatchSize = currentBatch.size();
            
            logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Processing batch %1/%2 for sensor [%3], batch size: %4")
                     .arg(batchNumber)
                     .arg((cache->filteredDataItems.size() + dbBatchSize - 1) / dbBatchSize)
                     .arg(cache->sensorId)
                     .arg(currentBatchSize));
            
            // 处理当前批次
            UploadDataHandler batchHandler;
            batchHandler.setSensorId(cache->sensorId);
            batchHandler.addData(currentBatch);

            bool parseResult = batchHandler.parseData();
            if (!parseResult) {
                logError(QString("UploadDataBackgroundProcessor::processSensorCache - Parse failed for batch %1, sensor [%2], batch size: %3")
                         .arg(batchNumber).arg(cache->sensorId).arg(currentBatchSize));
                failedBatches++;
            } else {
                logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Parse successful for batch %1, sensor [%2]")
                         .arg(batchNumber).arg(cache->sensorId));
            }
            
            //yieldCpu();

            bool saveResult = batchHandler.saveDataToDB();
            if (!saveResult) {
                logError(QString("UploadDataBackgroundProcessor::processSensorCache - Save to DB failed for batch %1, sensor [%2], batch size: %3")
                         .arg(batchNumber).arg(cache->sensorId).arg(currentBatchSize));
                failedBatches++;
            } else {
                logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Save to DB successful for batch %1, sensor [%2]")
                         .arg(batchNumber).arg(cache->sensorId));
                successfulBatches++;
            }
            
            // 统计处理结果
            if (parseResult && saveResult) {
                totalProcessed += currentBatchSize;
                logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Batch %1 completed successfully, processed %2/%3 items for sensor [%4]")
                         .arg(batchNumber).arg(totalProcessed).arg(cache->filteredDataItems.size()).arg(cache->sensorId));
            }

            //更新同步计数
            if (!m_syncTask.isNull()) {
                m_syncTask->updateSensorSyncedItemCount(cache->sensorId, currentBatchSize);
                logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Updated sync count for sensor [%1], currentBatchSize: %2")
                         .arg(cache->sensorId).arg(currentBatchSize));
                emit dataProcessed();
            }

            //释放内存
            currentBatch.clear();
            //释放信号量
            m_spaceSemaphore.release(currentBatchSize);

            //yieldCpu(); // 每批处理完让出CPU

            if (!m_isRunning) {
                logWarnning(QString("UploadDataBackgroundProcessor::processSensorCache - Processing stopped, sensor [%1], processed %2/%3 items")
                            .arg(cache->sensorId).arg(totalProcessed).arg(cache->filteredDataItems.size()));
                break;
            }
        }
    }
    
    // 输出最终处理结果统计
    logInfo(QString("UploadDataBackgroundProcessor::processSensorCache - Sensor [%1] processing completed: total items [%2], processed [%3], successful batches [%4], failed batches [%5]")
            .arg(cache->sensorId)
            .arg(cache->filteredDataItems.size())
            .arg(totalProcessed)
            .arg(successfulBatches)
            .arg(failedBatches));
    
    // 更新同步计数
//    if (!m_syncTask.isNull()) {
//        m_syncTask->updateSensorSyncedItemCount(cache->sensorId, cache->totalOriginalCount);
//        logTrace(QString("UploadDataBackgroundProcessor::processSensorCache - Updated sync count for sensor [%1], total original count: %2")
//                 .arg(cache->sensorId).arg(cache->totalOriginalCount));
//    }
}

QList<QSharedPointer<DataItem>> UploadDataBackgroundProcessor::deduplicateData(const QString& sensorId, 
                                                                              const QList<QSharedPointer<DataItem>>& dataItems)
{
    if (m_syncTask.isNull()) {
        logWarnning("UploadDataBackgroundProcessor::deduplicateData - SyncTask is null, cannot deduplicate");
        return dataItems;
    }
    
    // 获取传感器已存在的数据ID信息
    const SyncTask::SensorSyncInfo sensorSyncInfo = m_syncTask->getSensorSyncInfo(sensorId);
    logTrace(QString("UploadDataBackgroundProcessor::deduplicateData - Sensor [%1] has %2 existing data IDs")
             .arg(sensorId).arg(sensorSyncInfo.existingDataIds.size()));
    
    QList<QSharedPointer<DataItem>> filteredDataItems;

    for (int i = 0; i < dataItems.size(); ++i) {
        const auto& dataItem = dataItems[i];
        if (!sensorSyncInfo.existingDataIds.contains(dataItem->getDataId())) {
            filteredDataItems.append(dataItem);
        }
        else {
            logTrace(QString("UploadDataBackgroundProcessor::deduplicateData - Sensor [%1], Data ID [%2] already exists, skipped")
                     .arg(sensorId, QString::number(dataItem->getDataId())));
        }

        // 每处理10个数据项就让出CPU
//        if (i % 10 == 0) {
//            yieldCpu();
//        }
    }
    
    return filteredDataItems;
}

void UploadDataBackgroundProcessor::yieldCpu()
{
    // 让出CPU时间片，让其他线程有机会执行
    QThread::msleep(5);
    QCoreApplication::processEvents();
}


